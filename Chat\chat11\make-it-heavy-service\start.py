#!/usr/bin/env python3
"""
Startup script for the Make-it-Heavy FastAPI service
"""
import os
import sys
import uvicorn
from dotenv import load_dotenv

def main():
    # Load environment variables
    load_dotenv()
    
    # Get configuration from environment or use defaults
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    log_level = os.getenv("LOG_LEVEL", "info")
    
    print(f"Starting Make-it-Heavy Service on {host}:{port}")
    print(f"Log level: {log_level}")
    
    # Check if make-it-heavy directory exists
    make_it_heavy_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy')
    if not os.path.exists(make_it_heavy_path):
        print(f"ERROR: make-it-heavy directory not found at {make_it_heavy_path}")
        print("Please ensure the make-it-heavy directory is in the correct location")
        sys.exit(1)
    
    # Check if config file exists
    config_path = os.path.join(make_it_heavy_path, 'config.yaml')
    if not os.path.exists(config_path):
        print(f"ERROR: config.yaml not found at {config_path}")
        print("Please ensure the config.yaml file exists in the make-it-heavy directory")
        sys.exit(1)
    
    print(f"Using config file: {config_path}")
    
    # Start the server
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        log_level=log_level,
        reload=True,  # Enable auto-reload for development
        access_log=True
    )

if __name__ == "__main__":
    main()
