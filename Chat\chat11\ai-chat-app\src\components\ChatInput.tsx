'use client';

import { useState, useRef, useEffect } from 'react';
import { PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import CompactModeSelector, { ChatMode } from './CompactModeSelector';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  currentMode?: ChatMode;
  onModeChange?: (mode: ChatMode) => void;
}

export default function ChatInput({ 
  onSendMessage, 
  disabled = false,
  placeholder = "Type your message...",
  currentMode = 'simple',
  onModeChange
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  return (
    <div className="border-t border-gray-200 bg-white p-3">
      <form onSubmit={handleSubmit} className="flex gap-2 items-end">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={cn(
              "w-full resize-none rounded-lg border border-gray-300 px-3 py-2 pr-10",
              "focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",
              "disabled:bg-gray-100 disabled:cursor-not-allowed",
              "max-h-32 overflow-y-auto"
            )}
            style={{ minHeight: '40px' }}
          />
          
          <button
            type="submit"
            disabled={!message.trim() || disabled}
            className={cn(
              "absolute right-1.5 bottom-1.5 p-1.5 rounded-lg transition-colors",
              "disabled:text-gray-400 disabled:cursor-not-allowed",
              "enabled:text-blue-600 enabled:hover:bg-blue-50"
            )}
          >
            <PaperAirplaneIcon className="w-4 h-4" />
          </button>
        </div>
      </form>
      

      
      {/* Compact Mode Selector */}
      {onModeChange && (
        <div className="mt-2">
          <CompactModeSelector
            currentMode={currentMode}
            onModeChange={onModeChange}
            disabled={disabled}
          />
        </div>
      )}
    </div>
  );
}
