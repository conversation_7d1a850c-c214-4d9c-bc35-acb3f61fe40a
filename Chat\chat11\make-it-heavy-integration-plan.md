# Make-it-Heavy Integration Plan with AI Chat App

## Overview

This document outlines the integration strategy for incorporating the make-it-heavy orchestrator and agent capabilities into the existing ai-chat-app. The integration will provide enhanced AI capabilities including multi-agent orchestration, tool usage, and advanced research capabilities while maintaining the existing ChatGPT-like user experience.

## Current Architecture Analysis

### AI Chat App (Frontend)
- **Framework**: Next.js 15 with TypeScript
- **UI**: React components with Tailwind CSS
- **State Management**: Custom hooks (useChat)
- **Storage**: Local browser storage
- **API Communication**: Direct fetch to Chutes AI API with streaming support

### Make-it-Heavy (Backend Enhancement)
- **Framework**: Python with OpenAI client library
- **Capabilities**: 
  - Single agent with tool support
  - Multi-agent orchestration
  - Dynamic tool discovery
  - Web search, file operations, calculations
- **API**: Now configured for Chutes AI compatibility

## Integration Architecture

### Approach 1: Python Backend Service (Recommended)

#### Architecture Overview
```
Frontend (Next.js) → Backend API Routes → Python Service → Chutes AI
                                      ↓
                                   Tools & Orchestrator
```

#### Components

1. **Python Service Layer**
   - FastAPI or Flask service running make-it-heavy
   - Exposes REST endpoints for agent and orchestrator functionality
   - Handles tool execution and multi-agent coordination
   - Streams responses back to Next.js backend

2. **Next.js API Routes**
   - Proxy layer between frontend and Python service
   - Maintains existing streaming interface
   - Handles authentication and error management
   - Provides fallback to direct Chutes API if Python service unavailable

3. **Enhanced Frontend**
   - New UI components for advanced features
   - Mode selector (Simple Chat vs Enhanced Research)
   - Progress indicators for multi-agent operations
   - Tool execution feedback

### Approach 2: Node.js Port (Alternative)

#### Architecture Overview
```
Frontend (Next.js) → Enhanced API Routes → Chutes AI
                           ↓
                    Node.js Tool System
```

#### Components

1. **Node.js Tool System**
   - Port Python tools to TypeScript/JavaScript
   - Implement orchestrator logic in Node.js
   - Use existing Next.js API routes

2. **Challenges**
   - Significant development effort to port Python code
   - Loss of existing Python ecosystem tools
   - Potential performance differences

## Recommended Implementation Plan

### Phase 1: Python Service Setup

#### 1.1 Create FastAPI Service
```python
# make-it-heavy-service/main.py
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from agent import ChutesAgent
from orchestrator import TaskOrchestrator
import json
import asyncio

app = FastAPI()

class ChatRequest(BaseModel):
    messages: list
    mode: str = "single"  # "single" or "orchestrator"
    stream: bool = True

@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    # Implementation details in Phase 1.2
    pass
```

#### 1.2 Implement Streaming Endpoints
- `/chat/single` - Single agent with tools
- `/chat/orchestrator` - Multi-agent orchestration
- `/chat/stream` - Server-sent events for real-time updates

#### 1.3 Service Configuration
- Environment-based configuration
- Health check endpoints
- Logging and monitoring
- Docker containerization

### Phase 2: Next.js Integration

#### 2.1 New API Routes
```typescript
// src/app/api/enhanced-chat/route.ts
export async function POST(request: NextRequest) {
  const body = await request.json();
  
  // Proxy to Python service
  const pythonServiceUrl = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';
  
  // Stream response from Python service
  // Implementation details in Phase 2.2
}
```

#### 2.2 Enhanced API Service
- Extend existing APIService class
- Add mode selection (simple vs enhanced)
- Implement fallback mechanisms
- Handle Python service errors gracefully

#### 2.3 Frontend Enhancements
- Mode selector component
- Progress indicators for orchestrator
- Tool execution status display
- Enhanced error handling

### Phase 3: Advanced Features

#### 3.1 Tool Management UI
- Display available tools
- Tool execution history
- Custom tool configuration

#### 3.2 Orchestrator Controls
- Agent count configuration
- Timeout settings
- Aggregation strategy selection

#### 3.3 Research Mode
- Specialized UI for research tasks
- Source citation display
- Export functionality

## Technical Specifications

### API Endpoints

#### Python Service Endpoints

```
POST /chat/single
{
  "messages": [{"role": "user", "content": "query"}],
  "stream": true,
  "config": {
    "max_iterations": 10,
    "tools_enabled": true
  }
}

POST /chat/orchestrator
{
  "query": "research query",
  "stream": true,
  "config": {
    "parallel_agents": 4,
    "timeout": 300,
    "aggregation_strategy": "consensus"
  }
}

GET /health
GET /tools
GET /config
```

#### Next.js API Routes

```
POST /api/enhanced-chat
{
  "messages": [...],
  "mode": "single" | "orchestrator",
  "stream": true,
  "config": {...}
}

GET /api/enhanced-chat/status
GET /api/enhanced-chat/tools
```

### Data Flow

1. **User Input** → Frontend captures message
2. **Mode Detection** → Determine if enhanced features needed
3. **API Route** → Next.js API route receives request
4. **Service Call** → Proxy to Python service if enhanced mode
5. **Processing** → Python service executes agent/orchestrator
6. **Streaming** → Real-time response streaming
7. **Frontend Update** → UI updates with streamed content

### Configuration Management

#### Environment Variables
```bash
# Next.js (.env.local)
NEXT_PUBLIC_CHUTES_API_TOKEN=your_token
PYTHON_SERVICE_URL=http://localhost:8000
ENHANCED_MODE_ENABLED=true

# Python Service (.env)
CHUTES_API_KEY=your_token
CHUTES_BASE_URL=https://llm.chutes.ai/v1
CHUTES_MODEL=moonshotai/Kimi-K2-Instruct
```

#### Service Discovery
- Health check mechanisms
- Automatic fallback to simple mode
- Service restart handling

## Deployment Strategy

### Development Environment
1. Run Python service locally (`uvicorn main:app --reload`)
2. Run Next.js app (`npm run dev`)
3. Services communicate via localhost

### Production Environment
1. **Docker Compose** setup with both services
2. **Kubernetes** deployment with service mesh
3. **Serverless** functions for Python service (AWS Lambda, Vercel Functions)

### Scaling Considerations
- Python service horizontal scaling
- Load balancing for multiple agents
- Resource management for orchestrator operations
- Caching strategies for tool results

## Testing Strategy

### Unit Tests
- Python service endpoint tests
- Next.js API route tests
- Component integration tests

### Integration Tests
- End-to-end chat flow
- Streaming functionality
- Error handling scenarios
- Fallback mechanisms

### Performance Tests
- Concurrent user handling
- Multi-agent orchestration load
- Memory usage optimization
- Response time benchmarks

## Security Considerations

### API Security
- Authentication between services
- Rate limiting
- Input validation and sanitization
- CORS configuration

### Data Privacy
- Message content handling
- API key management
- User data isolation
- Audit logging

## Migration Strategy

### Backward Compatibility
- Existing chat functionality unchanged
- Gradual feature rollout
- User preference settings
- A/B testing capabilities

### Feature Flags
- Enhanced mode toggle
- Tool-specific enablement
- Orchestrator availability
- Performance monitoring

## Success Metrics

### Technical Metrics
- Response time improvements
- Error rate reduction
- Service availability
- Resource utilization

### User Experience Metrics
- Feature adoption rates
- User satisfaction scores
- Task completion rates
- Support ticket reduction

## Implementation Examples

### Python FastAPI Service Example

```python
# make-it-heavy-service/main.py
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, AsyncGenerator
import json
import asyncio
from agent import ChutesAgent
from orchestrator import TaskOrchestrator

app = FastAPI(title="Make-it-Heavy Service", version="1.0.0")

# CORS middleware for Next.js integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    mode: str = "single"
    stream: bool = True
    config: Optional[dict] = None

async def stream_agent_response(agent, query: str) -> AsyncGenerator[str, None]:
    """Stream agent response with proper formatting"""
    try:
        # This would need to be modified to support async streaming
        response = agent.run(query)

        # Simulate streaming by chunking the response
        words = response.split()
        for i, word in enumerate(words):
            chunk_data = {
                "choices": [{
                    "delta": {"content": word + " "},
                    "finish_reason": None
                }]
            }
            yield f"data: {json.dumps(chunk_data)}\n\n"
            await asyncio.sleep(0.1)  # Simulate processing time

        # Send completion signal
        final_chunk = {
            "choices": [{
                "delta": {},
                "finish_reason": "stop"
            }]
        }
        yield f"data: {json.dumps(final_chunk)}\n\n"
        yield "data: [DONE]\n\n"

    except Exception as e:
        error_chunk = {
            "error": {"message": str(e), "type": "agent_error"}
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"

@app.post("/chat/single")
async def single_agent_chat(request: ChatRequest):
    """Single agent endpoint with tool support"""
    try:
        agent = ChutesAgent(silent=True)
        query = request.messages[-1].content

        if request.stream:
            return StreamingResponse(
                stream_agent_response(agent, query),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache"}
            )
        else:
            response = agent.run(query)
            return {"response": response}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/orchestrator")
async def orchestrator_chat(request: ChatRequest):
    """Multi-agent orchestrator endpoint"""
    try:
        orchestrator = TaskOrchestrator(silent=True)
        query = request.messages[-1].content

        if request.stream:
            # Similar streaming implementation for orchestrator
            response = orchestrator.orchestrate(query)
            return StreamingResponse(
                stream_agent_response(None, response),
                media_type="text/plain"
            )
        else:
            response = orchestrator.orchestrate(query)
            return {"response": response}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "make-it-heavy"}

@app.get("/tools")
async def get_available_tools():
    """Return list of available tools"""
    try:
        agent = ChutesAgent(silent=True)
        tools = list(agent.discovered_tools.keys())
        return {"tools": tools}
    except Exception as e:
        return {"error": str(e), "tools": []}
```

### Next.js Enhanced API Route Example

```typescript
// src/app/api/enhanced-chat/route.ts
import { NextRequest, NextResponse } from 'next/server';

const PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { messages, mode = 'single', stream = true, config = {} } = body;

    // Validate request
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: 'Invalid messages format' }, { status: 400 });
    }

    // Determine endpoint based on mode
    const endpoint = mode === 'orchestrator' ? '/chat/orchestrator' : '/chat/single';
    const serviceUrl = `${PYTHON_SERVICE_URL}${endpoint}`;

    // Check if Python service is available
    try {
      const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (!healthCheck.ok) {
        throw new Error('Service unavailable');
      }
    } catch (error) {
      // Fallback to direct Chutes API
      console.warn('Python service unavailable, falling back to direct API');
      return fallbackToDirectAPI(messages, stream);
    }

    // Forward request to Python service
    const response = await fetch(serviceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        mode,
        stream,
        config
      })
    });

    if (!response.ok) {
      throw new Error(`Python service error: ${response.status}`);
    }

    if (stream && response.body) {
      // Stream response back to client
      return new Response(response.body, {
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      const data = await response.json();
      return NextResponse.json(data);
    }

  } catch (error) {
    console.error('Enhanced chat API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function fallbackToDirectAPI(messages: any[], stream: boolean) {
  // Import existing APIService for fallback
  const { APIService } = await import('@/lib/api');

  if (stream) {
    // Implement streaming fallback
    const encoder = new TextEncoder();
    const readable = new ReadableStream({
      async start(controller) {
        try {
          await APIService.sendMessage(
            messages,
            (chunk: string) => {
              const chunkData = {
                choices: [{
                  delta: { content: chunk },
                  finish_reason: null
                }]
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`));
            },
            () => {
              controller.enqueue(encoder.encode('data: [DONE]\n\n'));
              controller.close();
            },
            (error: string) => {
              const errorData = { error: { message: error } };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
              controller.close();
            }
          );
        } catch (error) {
          controller.error(error);
        }
      }
    });

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache',
      },
    });
  } else {
    const response = await APIService.sendNonStreamingMessage(messages);
    return NextResponse.json({ response });
  }
}
```

### Frontend Enhancement Example

```typescript
// src/lib/enhanced-api.ts
import { APIMessage } from '@/types/chat';

export interface EnhancedChatConfig {
  mode: 'single' | 'orchestrator';
  maxIterations?: number;
  parallelAgents?: number;
  timeout?: number;
  toolsEnabled?: boolean;
}

export class EnhancedAPIService {
  private static readonly API_URL = '/api/enhanced-chat';

  static async sendEnhancedMessage(
    messages: APIMessage[],
    config: EnhancedChatConfig,
    onChunk?: (content: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
    onProgress?: (status: string) => void
  ): Promise<string> {
    try {
      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages,
          mode: config.mode,
          stream: true,
          config: {
            max_iterations: config.maxIterations,
            parallel_agents: config.parallelAgents,
            timeout: config.timeout,
            tools_enabled: config.toolsEnabled
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body received');
      }

      return await this.processEnhancedStream(
        response.body,
        onChunk,
        onComplete,
        onError,
        onProgress
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onError?.(errorMessage);
      throw error;
    }
  }

  private static async processEnhancedStream(
    body: ReadableStream<Uint8Array>,
    onChunk?: (content: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
    onProgress?: (status: string) => void
  ): Promise<string> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          onComplete?.();
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        const parts = buffer.split('\n\n');
        buffer = parts.pop() || '';

        for (const part of parts) {
          const lines = part.split('\n');

          for (const line of lines) {
            const trimmedLine = line.trim();

            if (trimmedLine.startsWith('data: ')) {
              const data = trimmedLine.slice(6).trim();

              if (data === '[DONE]') {
                onComplete?.();
                return fullContent;
              }

              if (data === '' || data === '{}') {
                continue;
              }

              try {
                const parsed = JSON.parse(data);

                // Handle error responses
                if (parsed.error) {
                  onError?.(parsed.error.message);
                  return fullContent;
                }

                // Handle progress updates
                if (parsed.progress) {
                  onProgress?.(parsed.progress);
                  continue;
                }

                // Handle content chunks
                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  fullContent += content;
                  onChunk?.(content);
                }

                // Check for completion
                if (parsed.choices?.[0]?.finish_reason === 'stop') {
                  onComplete?.();
                  return fullContent;
                }
              } catch (parseError) {
                console.warn('Failed to parse chunk:', data, parseError);
              }
            }
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Stream processing error';
      onError?.(errorMessage);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return fullContent;
  }

  static async getAvailableTools(): Promise<string[]> {
    try {
      const response = await fetch('/api/enhanced-chat/tools');
      if (!response.ok) {
        throw new Error('Failed to fetch tools');
      }
      const data = await response.json();
      return data.tools || [];
    } catch (error) {
      console.error('Error fetching tools:', error);
      return [];
    }
  }

  static async checkServiceHealth(): Promise<boolean> {
    try {
      const response = await fetch('/api/enhanced-chat/health');
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}
```

## Next Steps

1. **Immediate**: Set up Python FastAPI service using the example above
2. **Week 1**: Implement basic streaming endpoints and test with standalone script
3. **Week 2**: Create Next.js integration layer with fallback mechanisms
4. **Week 3**: Add frontend enhancements and mode selection UI
5. **Week 4**: Testing and optimization with both services
6. **Week 5**: Production deployment preparation and documentation

## Conclusion

This integration plan provides a comprehensive approach to enhancing the ai-chat-app with make-it-heavy's advanced capabilities while maintaining the existing user experience. The phased approach allows for incremental development and testing, ensuring a smooth transition and robust final product. The provided code examples demonstrate the practical implementation details for each component of the integration.
