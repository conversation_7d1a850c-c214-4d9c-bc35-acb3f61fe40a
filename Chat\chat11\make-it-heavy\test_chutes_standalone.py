#!/usr/bin/env python3
"""
Standalone test script for make-it-heavy with Chutes AI API
This script allows testing the agent and orchestrator functionality independently
"""

import os
import sys
import yaml
import argparse
from agent import ChutesAgent
from orchestrator import TaskOrchestrator

def load_config(config_path="config.yaml"):
    """Load configuration from YAML file"""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"Error: Configuration file '{config_path}' not found.")
        print("Please make sure config.yaml exists in the current directory.")
        sys.exit(1)
    except yaml.YAMLError as e:
        print(f"Error parsing configuration file: {e}")
        sys.exit(1)

def validate_config(config):
    """Validate that required configuration is present"""
    if 'chutes' not in config:
        print("Error: 'chutes' section missing from config.yaml")
        return False
    
    chutes_config = config['chutes']
    if not chutes_config.get('api_key') or chutes_config['api_key'] == 'YOUR_CHUTES_API_KEY':
        print("Error: Please set your Chutes API key in config.yaml")
        print("Update the 'api_key' field under 'chutes' section")
        return False
    
    required_fields = ['base_url', 'model']
    for field in required_fields:
        if not chutes_config.get(field):
            print(f"Error: Missing required field 'chutes.{field}' in config.yaml")
            return False
    
    return True

def test_single_agent(query, config_path="config.yaml"):
    """Test single agent functionality"""
    print("=" * 60)
    print("TESTING SINGLE AGENT")
    print("=" * 60)
    
    config = load_config(config_path)
    if not validate_config(config):
        return False
    
    print(f"Query: {query}")
    print(f"Model: {config['chutes']['model']}")
    print(f"Base URL: {config['chutes']['base_url']}")
    print("-" * 60)
    
    try:
        agent = ChutesAgent(config_path, silent=False)
        print("Agent initialized successfully!")
        print("Processing query...")
        
        response = agent.run(query)
        
        print("\n" + "=" * 60)
        print("AGENT RESPONSE:")
        print("=" * 60)
        print(response)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"Error running single agent: {e}")
        return False

def test_orchestrator(query, config_path="config.yaml"):
    """Test orchestrator functionality with multiple agents"""
    print("=" * 60)
    print("TESTING ORCHESTRATOR (MULTIPLE AGENTS)")
    print("=" * 60)
    
    config = load_config(config_path)
    if not validate_config(config):
        return False
    
    print(f"Query: {query}")
    print(f"Model: {config['chutes']['model']}")
    print(f"Parallel Agents: {config.get('orchestrator', {}).get('parallel_agents', 4)}")
    print(f"Base URL: {config['chutes']['base_url']}")
    print("-" * 60)
    
    try:
        orchestrator = TaskOrchestrator(config_path, silent=False)
        print("Orchestrator initialized successfully!")
        print("Processing query with multiple agents...")
        
        response = orchestrator.orchestrate(query)
        
        print("\n" + "=" * 60)
        print("ORCHESTRATOR RESPONSE:")
        print("=" * 60)
        print(response)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"Error running orchestrator: {e}")
        return False

def interactive_mode():
    """Run in interactive mode for continuous testing"""
    print("=" * 60)
    print("INTERACTIVE MODE")
    print("=" * 60)
    print("Commands:")
    print("  /agent <query>       - Test single agent")
    print("  /orchestrator <query> - Test orchestrator")
    print("  /quit or /exit       - Exit interactive mode")
    print("  <query>              - Test single agent (default)")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['/quit', '/exit']:
                print("Goodbye!")
                break
                
            if user_input.startswith('/agent '):
                query = user_input[7:].strip()
                if query:
                    test_single_agent(query)
                else:
                    print("Please provide a query after /agent")
                    
            elif user_input.startswith('/orchestrator '):
                query = user_input[14:].strip()
                if query:
                    test_orchestrator(query)
                else:
                    print("Please provide a query after /orchestrator")
                    
            else:
                # Default to single agent
                test_single_agent(user_input)
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Test make-it-heavy with Chutes AI API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_chutes_standalone.py --interactive
  python test_chutes_standalone.py --agent "What is the weather like today?"
  python test_chutes_standalone.py --orchestrator "Research the latest AI developments"
  python test_chutes_standalone.py --config custom_config.yaml --agent "Hello world"
        """
    )
    
    parser.add_argument('--config', '-c', default='config.yaml',
                       help='Path to configuration file (default: config.yaml)')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('--agent', '-a', type=str,
                       help='Test single agent with the given query')
    parser.add_argument('--orchestrator', '-o', type=str,
                       help='Test orchestrator with the given query')
    
    args = parser.parse_args()
    
    # Check if config file exists
    if not os.path.exists(args.config):
        print(f"Error: Configuration file '{args.config}' not found.")
        sys.exit(1)
    
    print("Make-it-Heavy Chutes AI Test Script")
    print("=" * 60)
    
    if args.interactive:
        interactive_mode()
    elif args.agent:
        success = test_single_agent(args.agent, args.config)
        sys.exit(0 if success else 1)
    elif args.orchestrator:
        success = test_orchestrator(args.orchestrator, args.config)
        sys.exit(0 if success else 1)
    else:
        print("No action specified. Use --help for usage information.")
        print("Quick start: python test_chutes_standalone.py --interactive")

if __name__ == "__main__":
    main()
