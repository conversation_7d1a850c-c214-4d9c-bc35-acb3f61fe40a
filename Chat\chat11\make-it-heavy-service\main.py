import os
import sys
import json
import asyncio
from typing import List, Optional, Dict, Any, AsyncGenerator
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging

# Add the make-it-heavy directory to the path so we can import the modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy'))

try:
    from agent import ChutesAgent
    from orchestrator import TaskOrchestrator
except ImportError as e:
    print(f"Error importing make-it-heavy modules: {e}")
    print("Make sure the make-it-heavy directory is in the correct location")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Make-it-Heavy Service",
    description="Enhanced AI chat service with tool support and multi-agent orchestration",
    version="1.0.0"
)

# CORS middleware for Next.js integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    mode: str = "single"  # "single" or "orchestrator"
    stream: bool = True
    config: Optional[Dict[str, Any]] = None

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

class ToolsResponse(BaseModel):
    tools: List[str]
    count: int

async def stream_response_chunks(content: str, chunk_size: int = 50) -> AsyncGenerator[str, None]:
    """Stream response content in chunks to simulate real-time streaming"""
    if not content:
        # Send empty completion if no content
        final_chunk = {
            "choices": [{
                "delta": {},
                "finish_reason": "stop"
            }]
        }
        yield f"data: {json.dumps(final_chunk)}\n\n"
        yield "data: [DONE]\n\n"
        return

    # Stream by character chunks instead of words to preserve formatting
    for i in range(0, len(content), chunk_size):
        chunk_content = content[i:i + chunk_size]
        
        chunk_data = {
            "choices": [{
                "delta": {"content": chunk_content},
                "finish_reason": None
            }]
        }
        yield f"data: {json.dumps(chunk_data)}\n\n"
        await asyncio.sleep(0.02)  # Smaller delay for smoother streaming

    # Send completion signal
    final_chunk = {
        "choices": [{
            "delta": {},
            "finish_reason": "stop"
        }]
    }
    yield f"data: {json.dumps(final_chunk)}\n\n"
    yield "data: [DONE]\n\n"

async def stream_agent_response(agent: ChutesAgent, query: str) -> AsyncGenerator[str, None]:
    """Stream agent response with proper error handling"""
    try:
        logger.info(f"Starting agent processing for query: {query[:100]}...")
        
        # Run the agent (this is synchronous, but we'll handle it)
        response = agent.run(query)
        
        logger.info(f"Agent completed, response length: {len(response)}")
        
        # Stream the response
        async for chunk in stream_response_chunks(response):
            yield chunk
            
    except Exception as e:
        logger.error(f"Agent error: {str(e)}")
        error_chunk = {
            "error": {"message": str(e), "type": "agent_error"}
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"

async def stream_orchestrator_response(orchestrator: TaskOrchestrator, query: str) -> AsyncGenerator[str, None]:
    """Stream orchestrator response with progress updates"""
    try:
        logger.info(f"Starting orchestrator processing for query: {query[:100]}...")

        # Send initial progress update
        progress_chunk = {
            "progress": "🚀 Starting multi-agent orchestration..."
        }
        yield f"data: {json.dumps(progress_chunk)}\n\n"
        await asyncio.sleep(0.5)

        # Send decomposition progress
        progress_chunk = {
            "progress": "🧠 Breaking down query into specialized tasks..."
        }
        yield f"data: {json.dumps(progress_chunk)}\n\n"
        await asyncio.sleep(0.5)

        # Send agent launch progress
        progress_chunk = {
            "progress": "⚡ Launching parallel agents for research..."
        }
        yield f"data: {json.dumps(progress_chunk)}\n\n"
        await asyncio.sleep(0.5)

        # Run the orchestrator (this is synchronous, but we'll handle it)
        response = orchestrator.orchestrate(query)

        logger.info(f"Orchestrator completed, response length: {len(response)}")

        # Send synthesis progress
        progress_chunk = {
            "progress": "🔄 Synthesizing results from all agents..."
        }
        yield f"data: {json.dumps(progress_chunk)}\n\n"
        await asyncio.sleep(0.5)

        # Stream the final response
        async for chunk in stream_response_chunks(response):
            yield chunk

    except Exception as e:
        logger.error(f"Orchestrator error: {str(e)}")
        error_chunk = {
            "error": {"message": str(e), "type": "orchestrator_error"}
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"

@app.post("/chat/single")
async def single_agent_chat(request: ChatRequest):
    """Single agent endpoint with tool support"""
    try:
        logger.info(f"Single agent request: {len(request.messages)} messages, stream={request.stream}")
        
        # Get the latest user message
        if not request.messages:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        query = request.messages[-1].content
        
        # Initialize agent with config from make-it-heavy directory
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)
        
        if request.stream:
            return StreamingResponse(
                stream_agent_response(agent, query),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )
        else:
            response = agent.run(query)
            return {"response": response}
            
    except Exception as e:
        logger.error(f"Single agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat/orchestrator")
async def orchestrator_chat(request: ChatRequest):
    """Multi-agent orchestrator endpoint"""
    try:
        logger.info(f"Orchestrator request: {len(request.messages)} messages, stream={request.stream}")
        
        # Get the latest user message
        if not request.messages:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        query = request.messages[-1].content
        
        # Initialize orchestrator with config from make-it-heavy directory
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        orchestrator = TaskOrchestrator(config_path=config_path, silent=True)
        
        if request.stream:
            return StreamingResponse(
                stream_orchestrator_response(orchestrator, query),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no"  # Disable nginx buffering
                }
            )
        else:
            response = orchestrator.orchestrate(query)
            return {"response": response}
            
    except Exception as e:
        logger.error(f"Orchestrator error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with detailed status"""
    try:
        # Test if we can initialize an agent (basic functionality test)
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)
        tools_count = len(agent.discovered_tools)

        return HealthResponse(
            status="healthy",
            service="make-it-heavy",
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.get("/status")
async def detailed_status():
    """Detailed status endpoint with more information"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)

        return {
            "status": "healthy",
            "service": "make-it-heavy",
            "version": "1.0.0",
            "config_path": config_path,
            "tools_available": len(agent.discovered_tools),
            "endpoints": [
                "/chat/single",
                "/chat/orchestrator",
                "/health",
                "/status",
                "/tools",
                "/config"
            ]
        }
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "service": "make-it-heavy",
            "version": "1.0.0"
        }

@app.get("/tools", response_model=ToolsResponse)
async def get_available_tools():
    """Return list of available tools"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)
        tools = list(agent.discovered_tools.keys())
        return ToolsResponse(tools=tools, count=len(tools))
    except Exception as e:
        logger.error(f"Error fetching tools: {str(e)}")
        return ToolsResponse(tools=[], count=0)

@app.get("/tools/detailed")
async def get_detailed_tools():
    """Return detailed information about available tools"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)

        tools_info = []
        for name, tool in agent.discovered_tools.items():
            tools_info.append({
                "name": name,
                "description": tool.description,
                "parameters": tool.parameters
            })

        return {
            "tools": tools_info,
            "count": len(tools_info)
        }
    except Exception as e:
        logger.error(f"Error fetching detailed tools: {str(e)}")
        return {"tools": [], "count": 0, "error": str(e)}

@app.get("/config")
async def get_config():
    """Return current configuration (without sensitive data)"""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'make-it-heavy', 'config.yaml')
        agent = ChutesAgent(config_path=config_path, silent=True)
        
        # Return safe config info
        return {
            "agent": {
                "max_iterations": agent.config.get('agent', {}).get('max_iterations', 10)
            },
            "orchestrator": {
                "parallel_agents": agent.config.get('orchestrator', {}).get('parallel_agents', 4),
                "task_timeout": agent.config.get('orchestrator', {}).get('task_timeout', 300)
            },
            "tools_count": len(agent.discovered_tools)
        }
    except Exception as e:
        logger.error(f"Error fetching config: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
