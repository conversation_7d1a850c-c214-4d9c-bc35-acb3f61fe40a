import { NextRequest, NextResponse } from 'next/server';

const PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';

export async function GET() {
  try {
    console.log('Fetching available tools from Python service');
    
    const response = await fetch(`${PYTHON_SERVICE_URL}/tools`, {
      method: 'GET',
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`Python service error: ${response.status}`);
    }

    const data = await response.json();
    console.log(`Retrieved ${data.count} tools from Python service`);
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching tools:', error);
    return NextResponse.json(
      { 
        tools: [], 
        count: 0, 
        error: error instanceof Error ? error.message : 'Failed to fetch tools',
        fallback: true
      }, 
      { status: 503 }
    );
  }
}

// Get detailed tool information
export async function POST(request: NextRequest) {
  try {
    const { detailed = false } = await request.json();
    
    const endpoint = detailed ? '/tools/detailed' : '/tools';
    console.log(`Fetching ${detailed ? 'detailed' : 'basic'} tools from Python service`);
    
    const response = await fetch(`${PYTHON_SERVICE_URL}${endpoint}`, {
      method: 'GET',
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      throw new Error(`Python service error: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching detailed tools:', error);
    return NextResponse.json(
      { 
        tools: [], 
        count: 0, 
        error: error instanceof Error ? error.message : 'Failed to fetch detailed tools',
        fallback: true
      }, 
      { status: 503 }
    );
  }
}
