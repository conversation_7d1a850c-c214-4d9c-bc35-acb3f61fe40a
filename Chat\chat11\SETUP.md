# Nexus AI Chat with Make-it-Heavy Integration - Setup Guide

This guide will help you set up the complete Nexus AI chat application with Make-it-Heavy integration, providing enhanced AI capabilities including tool access and multi-agent orchestration.

## Overview

The integration consists of three main components:

1. **Next.js Frontend** (`ai-chat-app/`) - The chat interface with mode selection
2. **Python FastAPI Service** (`make-it-heavy-service/`) - Backend service for enhanced features
3. **Make-it-Heavy** (`make-it-heavy/`) - AI agent system with tools and orchestration

## Prerequisites

- **Node.js 18+** and npm/yarn
- **Python 3.8+** with pip
- **Chutes AI API Token** - Get from [https://chutes.ai](https://chutes.ai)
- **Make-it-Heavy** - Properly installed and configured

## Step 1: Environment Setup

### 1.1 Next.js Application Configuration

```bash
cd ai-chat-app
cp .env.example .env.local
```

Edit `.env.local`:
```bash
# Required: Your Chutes AI API token
NEXT_PUBLIC_CHUTES_API_TOKEN=your_chutes_api_token_here

# Python service URL (default for local development)
PYTHON_SERVICE_URL=http://localhost:8000

# Optional: Enable fallback mode
ENABLE_FALLBACK=true
```

### 1.2 Python Service Configuration

```bash
cd make-it-heavy-service
cp .env.example .env
```

Edit `.env`:
```bash
# Service configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true

# Make-it-Heavy path
MAKE_IT_HEAVY_PATH=../make-it-heavy

# Logging
LOG_LEVEL=INFO
```

### 1.3 Make-it-Heavy Configuration

Ensure `make-it-heavy/config.yaml` is properly configured:
```yaml
chutes_ai:
  api_token: "your_chutes_api_token_here"
  model: "Qwen/Qwen3-235B-A22B-Thinking-2507"
  base_url: "https://api.chutes.ai/v1"
  max_tokens: 32768
  temperature: 0.7

orchestrator:
  max_agents: 5
  timeout: 300
  parallel_execution: true

system_prompts:
  default: "You are a helpful AI assistant with access to various tools..."
  orchestrator: "You are an AI orchestrator that coordinates multiple agents..."
```

## Step 2: Installation

### 2.1 Install Next.js Dependencies

```bash
cd ai-chat-app
npm install
```

### 2.2 Install Python Dependencies

```bash
cd make-it-heavy-service
pip install -r requirements.txt
```

## Step 3: Starting the Services

### 3.1 Start the Python Service (Terminal 1)

```bash
cd make-it-heavy-service
python start.py
```

The service will start on `http://localhost:8000` and perform validation checks:
- ✅ Make-it-Heavy directory exists
- ✅ Configuration file is valid
- ✅ Required modules can be imported
- ✅ Tools are discovered and loaded

### 3.2 Start the Next.js Application (Terminal 2)

```bash
cd ai-chat-app
npm run dev
```

The application will start on `http://localhost:3000`.

## Step 4: Verification

### 4.1 Check Service Health

Visit or curl the health endpoints:

```bash
# Python service health
curl http://localhost:8000/health

# Enhanced chat service health (via Next.js)
curl http://localhost:3000/api/enhanced-chat
```

### 4.2 Test Available Tools

```bash
# List available tools
curl http://localhost:8000/tools

# Get detailed tool information
curl http://localhost:8000/tools/detailed
```

### 4.3 Test Chat Modes

Open `http://localhost:3000` in your browser and test:

1. **Simple Chat Mode**: Basic AI responses (always available)
2. **Enhanced Chat Mode**: AI with tools and web search
3. **Research Mode**: Multi-agent deep analysis (if Python service is running)

## Features

### Chat Modes

- **Simple Chat**: Fast AI responses using direct Chutes API
- **Enhanced Chat**: AI with access to tools like web search, calculations, file operations
- **Research Mode**: Multi-agent orchestration for complex research queries

### Enhanced Features

- **Real-time Streaming**: All modes support streaming responses
- **Progress Indicators**: Visual feedback for enhanced and research modes
- **Automatic Fallback**: Falls back to simple mode if Python service is unavailable
- **Tool Discovery**: Dynamic loading and display of available tools
- **Health Monitoring**: Continuous service health checking
- **Rich Markdown Rendering**: AI responses are rendered with full markdown support including:
  - **Headers** with proper hierarchy and styling (H1-H4)
  - **Tables** with borders, headers, and responsive design
  - **Lists** (ordered and unordered) with proper indentation
  - **Code blocks** with dark theme and syntax highlighting
  - **Inline code** with highlighted background
  - **Links** that open in new tabs with hover effects
  - **Blockquotes** with left border and background styling
  - **Text formatting** (bold, italic) with proper emphasis
  - **GitHub Flavored Markdown (GFM)** support for advanced features

### User Interface

- **Mode Selector**: Easy switching between chat modes
- **Progress Tracking**: Real-time progress updates for complex operations
- **Enhanced Error Handling**: Detailed error messages with suggestions
- **Service Status**: Visual indicators for service availability

## Troubleshooting

### Common Issues

1. **Python Service Won't Start**
   - Check that make-it-heavy directory exists
   - Verify config.yaml is properly formatted
   - Ensure all Python dependencies are installed

2. **Enhanced Features Not Available**
   - Verify Python service is running on port 8000
   - Check PYTHON_SERVICE_URL in Next.js .env.local
   - Review browser console for connection errors

3. **API Token Issues**
   - Verify your Chutes AI API token is valid
   - Check token has sufficient credits
   - Ensure token is set in both config.yaml and .env.local

4. **Tool Loading Errors**
   - Check make-it-heavy tools directory exists
   - Verify tool files are properly formatted
   - Review Python service logs for import errors

### Logs and Debugging

- **Python Service Logs**: Check terminal output where you started the service
- **Next.js Logs**: Check browser console and terminal output
- **Network Issues**: Use browser dev tools to inspect API requests

### Fallback Behavior

If the Python service is unavailable:
- Simple Chat mode continues to work normally
- Enhanced Chat mode falls back to Simple Chat
- Research Mode becomes unavailable
- User receives clear notifications about service status

## Development

### Adding New Tools

1. Add tool files to `make-it-heavy/tools/`
2. Restart the Python service to discover new tools
3. Tools will automatically appear in the `/tools` endpoint

### Customizing Modes

- Modify `EnhancedAPIService` in `ai-chat-app/src/lib/enhanced-api.ts`
- Update mode configurations in the Python service
- Adjust UI components in `ai-chat-app/src/components/`

### Configuration Changes

- Python service: Restart required for most changes
- Next.js: Hot reload for most changes, restart for .env changes
- Make-it-Heavy: Restart Python service for config.yaml changes

## Production Deployment

### Python Service

- Use a production WSGI server like gunicorn
- Set up proper logging and monitoring
- Configure environment variables securely
- Implement rate limiting and authentication

### Next.js Application

- Build for production: `npm run build`
- Deploy to your preferred platform (Vercel, Netlify, etc.)
- Set environment variables in your deployment platform
- Configure proper CORS settings

### Security Considerations

- Keep API tokens secure and rotate regularly
- Implement proper authentication for production
- Use HTTPS in production
- Monitor API usage and costs

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review service logs for detailed error information
3. Verify all configuration files are properly set up
4. Test individual components (Python service, Next.js app) separately
