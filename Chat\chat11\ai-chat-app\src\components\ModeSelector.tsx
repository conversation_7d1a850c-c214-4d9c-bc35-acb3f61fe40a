'use client';

import { useState, useEffect } from 'react';
import { EnhancedAPIService } from '@/lib/enhanced-api';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator';

interface ModeSelectorProps {
  currentMode: ChatMode;
  onModeChange: (mode: ChatMode) => void;
  disabled?: boolean;
}

interface ModeOption {
  id: ChatMode;
  name: string;
  description: string;
  icon: string;
  available: boolean;
  badge?: string;
}

export default function ModeSelector({ currentMode, onModeChange, disabled = false }: ModeSelectorProps) {
  const [serviceStatus, setServiceStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    try {
      setIsLoading(true);
      const status = await EnhancedAPIService.checkServiceHealth();
      setServiceStatus(status);
    } catch (error) {
      console.error('Failed to check service status:', error);
      setServiceStatus(null);
    } finally {
      setIsLoading(false);
    }
  };

  const getModeOptions = (): ModeOption[] => {
    const enhancedAvailable = serviceStatus?.modes?.single === 'enhanced';
    const orchestratorAvailable = serviceStatus?.modes?.orchestrator === 'available';

    return [
      {
        id: 'simple',
        name: 'Simple Chat',
        description: 'Standard AI chat without tools',
        icon: '💬',
        available: true,
        badge: 'Fast'
      },
      {
        id: 'enhanced',
        name: 'Enhanced Chat',
        description: 'AI with web search, calculations, and file operations',
        icon: '🔧',
        available: true, // Always available (has fallback)
        badge: enhancedAvailable ? 'Tools' : 'Fallback'
      },
      {
        id: 'orchestrator',
        name: 'Research Mode',
        description: 'Multi-agent deep research and analysis',
        icon: '🧠',
        available: orchestratorAvailable,
        badge: orchestratorAvailable ? 'Advanced' : 'Unavailable'
      }
    ];
  };

  const modeOptions = getModeOptions();

  const handleModeSelect = (mode: ChatMode) => {
    if (disabled) return;
    
    const option = modeOptions.find(opt => opt.id === mode);
    if (option && option.available) {
      onModeChange(mode);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span className="text-sm text-gray-600">Checking service status...</span>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Chat Mode</h3>
        <button
          onClick={checkServiceStatus}
          className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
          title="Refresh service status"
        >
          🔄 Refresh
        </button>
      </div>
      
      <div className="grid grid-cols-1 gap-2">
        {modeOptions.map((option) => (
          <button
            key={option.id}
            onClick={() => handleModeSelect(option.id)}
            disabled={disabled || !option.available}
            className={`
              relative p-3 rounded-lg border text-left transition-all duration-200
              ${currentMode === option.id
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : option.available
                ? 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
              }
              ${disabled ? 'cursor-not-allowed opacity-50' : ''}
            `}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <span className="text-lg">{option.icon}</span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900">
                      {option.name}
                    </h4>
                    {option.badge && (
                      <span className={`
                        inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                        ${option.available
                          ? option.badge === 'Advanced'
                            ? 'bg-purple-100 text-purple-800'
                            : option.badge === 'Tools'
                            ? 'bg-green-100 text-green-800'
                            : option.badge === 'Fast'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-600'
                        }
                      `}>
                        {option.badge}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {option.description}
                  </p>
                </div>
              </div>
              
              {currentMode === option.id && (
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                </div>
              )}
            </div>
          </button>
        ))}
      </div>

      {serviceStatus && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Service Status:</span>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                serviceStatus.python_service === 'available' 
                  ? 'bg-green-400' 
                  : 'bg-yellow-400'
              }`}></div>
              <span>
                {serviceStatus.python_service === 'available' 
                  ? 'Enhanced features active' 
                  : 'Fallback mode active'
                }
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
