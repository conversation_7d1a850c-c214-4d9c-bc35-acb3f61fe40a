#!/usr/bin/env python3
"""
Integration Test Script for Nexus AI Chat with Make-it-Heavy

This script tests the complete integration between the Next.js frontend,
Python FastAPI service, and Make-it-Heavy backend.
"""

import asyncio
import json
import time
import requests
import aiohttp
from typing import Dict, Any, List
import sys
import os

# Configuration
PYTHON_SERVICE_URL = "http://localhost:8000"
NEXTJS_SERVICE_URL = "http://localhost:3000"

class IntegrationTester:
    def __init__(self):
        self.results = []
        self.python_service_available = False
        self.nextjs_service_available = False

    def log_result(self, test_name: str, success: bool, message: str, details: Dict = None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "details": details or {}
        })

    def test_python_service_health(self) -> bool:
        """Test Python service health endpoint"""
        try:
            response = requests.get(f"{PYTHON_SERVICE_URL}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.python_service_available = True
                self.log_result(
                    "Python Service Health", 
                    True, 
                    f"Service healthy: {data.get('status', 'unknown')}"
                )
                return True
            else:
                self.log_result(
                    "Python Service Health", 
                    False, 
                    f"HTTP {response.status_code}: {response.text}"
                )
                return False
        except Exception as e:
            self.log_result(
                "Python Service Health", 
                False, 
                f"Connection failed: {str(e)}"
            )
            return False

    def test_python_service_status(self) -> bool:
        """Test Python service detailed status"""
        if not self.python_service_available:
            self.log_result("Python Service Status", False, "Service not available")
            return False

        try:
            response = requests.get(f"{PYTHON_SERVICE_URL}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                tools_count = data.get('tools_available', 0)
                self.log_result(
                    "Python Service Status", 
                    True, 
                    f"Status OK, {tools_count} tools available",
                    {"status_data": data}
                )
                return True
            else:
                self.log_result(
                    "Python Service Status", 
                    False, 
                    f"HTTP {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_result(
                "Python Service Status", 
                False, 
                f"Request failed: {str(e)}"
            )
            return False

    def test_python_tools_endpoint(self) -> bool:
        """Test Python service tools endpoint"""
        if not self.python_service_available:
            self.log_result("Python Tools Endpoint", False, "Service not available")
            return False

        try:
            response = requests.get(f"{PYTHON_SERVICE_URL}/tools", timeout=10)
            if response.status_code == 200:
                data = response.json()
                tools_count = data.get('count', 0)
                tools = data.get('tools', [])
                self.log_result(
                    "Python Tools Endpoint", 
                    True, 
                    f"Found {tools_count} tools: {', '.join(tools[:5])}{'...' if len(tools) > 5 else ''}",
                    {"tools": tools}
                )
                return True
            else:
                self.log_result(
                    "Python Tools Endpoint", 
                    False, 
                    f"HTTP {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_result(
                "Python Tools Endpoint", 
                False, 
                f"Request failed: {str(e)}"
            )
            return False

    def test_nextjs_service_health(self) -> bool:
        """Test Next.js service health"""
        try:
            response = requests.get(f"{NEXTJS_SERVICE_URL}/api/enhanced-chat", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.nextjs_service_available = True
                python_status = data.get('python_service', 'unknown')
                self.log_result(
                    "Next.js Service Health", 
                    True, 
                    f"Service healthy, Python service: {python_status}",
                    {"health_data": data}
                )
                return True
            else:
                self.log_result(
                    "Next.js Service Health", 
                    False, 
                    f"HTTP {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_result(
                "Next.js Service Health", 
                False, 
                f"Connection failed: {str(e)}"
            )
            return False

    async def test_python_single_agent_streaming(self) -> bool:
        """Test Python service single agent streaming"""
        if not self.python_service_available:
            self.log_result("Python Single Agent Streaming", False, "Service not available")
            return False

        try:
            payload = {
                "messages": [{"role": "user", "content": "Hello, can you tell me what 2+2 equals?"}],
                "mode": "single",
                "stream": True
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{PYTHON_SERVICE_URL}/chat/single",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        chunks_received = 0
                        content_received = ""
                        
                        async for line in response.content:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str == '[DONE]':
                                    break
                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and data['choices']:
                                        content = data['choices'][0].get('delta', {}).get('content', '')
                                        if content:
                                            content_received += content
                                            chunks_received += 1
                                except json.JSONDecodeError:
                                    continue

                        success = chunks_received > 0 and len(content_received) > 0
                        self.log_result(
                            "Python Single Agent Streaming", 
                            success, 
                            f"Received {chunks_received} chunks, {len(content_received)} characters",
                            {"chunks": chunks_received, "content_length": len(content_received)}
                        )
                        return success
                    else:
                        self.log_result(
                            "Python Single Agent Streaming", 
                            False, 
                            f"HTTP {response.status}"
                        )
                        return False
        except Exception as e:
            self.log_result(
                "Python Single Agent Streaming", 
                False, 
                f"Request failed: {str(e)}"
            )
            return False

    async def test_nextjs_enhanced_chat_streaming(self) -> bool:
        """Test Next.js enhanced chat streaming"""
        if not self.nextjs_service_available:
            self.log_result("Next.js Enhanced Chat Streaming", False, "Service not available")
            return False

        try:
            payload = {
                "messages": [{"role": "user", "content": "What is 5 * 7?"}],
                "mode": "single",
                "stream": True
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{NEXTJS_SERVICE_URL}/api/enhanced-chat",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        chunks_received = 0
                        content_received = ""
                        
                        async for line in response.content:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str == '[DONE]':
                                    break
                                try:
                                    data = json.loads(data_str)
                                    if 'choices' in data and data['choices']:
                                        content = data['choices'][0].get('delta', {}).get('content', '')
                                        if content:
                                            content_received += content
                                            chunks_received += 1
                                except json.JSONDecodeError:
                                    continue

                        success = chunks_received > 0 and len(content_received) > 0
                        self.log_result(
                            "Next.js Enhanced Chat Streaming", 
                            success, 
                            f"Received {chunks_received} chunks, {len(content_received)} characters",
                            {"chunks": chunks_received, "content_length": len(content_received)}
                        )
                        return success
                    else:
                        self.log_result(
                            "Next.js Enhanced Chat Streaming", 
                            False, 
                            f"HTTP {response.status}"
                        )
                        return False
        except Exception as e:
            self.log_result(
                "Next.js Enhanced Chat Streaming", 
                False, 
                f"Request failed: {str(e)}"
            )
            return False

    def test_fallback_mechanism(self) -> bool:
        """Test fallback mechanism when Python service is unavailable"""
        # This test would require temporarily stopping the Python service
        # For now, we'll just verify the fallback configuration
        try:
            response = requests.get(f"{NEXTJS_SERVICE_URL}/api/enhanced-chat", timeout=10)
            if response.status_code == 200:
                data = response.json()
                fallback_available = data.get('fallback') == 'available'
                self.log_result(
                    "Fallback Mechanism", 
                    fallback_available, 
                    f"Fallback {'available' if fallback_available else 'not configured'}",
                    {"fallback_data": data}
                )
                return fallback_available
            else:
                self.log_result("Fallback Mechanism", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Fallback Mechanism", False, f"Request failed: {str(e)}")
            return False

    async def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting Nexus AI Chat Integration Tests\n")
        
        # Basic health checks
        print("📋 Basic Health Checks")
        self.test_python_service_health()
        self.test_python_service_status()
        self.test_python_tools_endpoint()
        self.test_nextjs_service_health()
        print()

        # Streaming tests
        print("🔄 Streaming Tests")
        await self.test_python_single_agent_streaming()
        await self.test_nextjs_enhanced_chat_streaming()
        print()

        # Fallback tests
        print("🔄 Fallback Tests")
        self.test_fallback_mechanism()
        print()

        # Summary
        self.print_summary()

    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - passed_tests

        print("📊 Test Summary")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")

        print("\n🎯 Integration Status:")
        if self.python_service_available and self.nextjs_service_available:
            print("✅ Full integration available - All modes should work")
        elif self.nextjs_service_available:
            print("⚠️  Partial integration - Simple mode available, enhanced features may fall back")
        else:
            print("❌ Integration not available - Services not responding")

async def main():
    """Main test runner"""
    tester = IntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    print("Nexus AI Chat Integration Test Suite")
    print("=" * 50)
    asyncio.run(main())
