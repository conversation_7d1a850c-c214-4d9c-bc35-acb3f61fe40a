'use client';

import { Message } from '@/types/chat';
import { UserIcon, CpuChipIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MessageBubbleProps {
  message: Message;
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  // Debug logging
  console.log('MessageBubble rendering:', {
    id: message.id,
    role: message.role,
    contentLength: message.content?.length || 0,
    isStreaming: message.isStreaming,
    timestamp: message.timestamp,
    contentPreview: message.content?.substring(0, 200),
    hasMarkdownChars: message.content ? /[*#`_\[\]\(\)]/.test(message.content) : false
  });

  return (
    <div className={cn(
      "flex gap-4 p-4",
      isUser ? "bg-transparent" : "bg-gray-50"
    )}>
      {/* Avatar */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser ? "bg-blue-600 text-white" : "bg-green-600 text-white"
      )}>
        {isUser ? (
          <UserIcon className="w-5 h-5" />
        ) : (
          <CpuChipIcon className="w-5 h-5" />
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1 min-w-0">
        {/* Role Label */}
        <div className="text-sm font-medium text-gray-600 mb-1">
          {isUser ? 'You' : 'Nexus'}
        </div>

        {/* Message Text */}
        <div className={cn(
          "text-gray-900",
          message.isStreaming && "animate-pulse"
        )}>
          {isUser ? (
            // User messages: render as plain text
            <div className="whitespace-pre-wrap break-words text-gray-900">
              {message.content}
            </div>
          ) : (
            // Assistant messages: render as markdown
            <div className="max-w-none prose prose-gray prose-sm">
              {(() => {
                console.log('Rendering markdown for content:', message.content?.substring(0, 100) + '...');
                try {
                  return (
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        // Headers
                        h1: ({ children }) => (
                          <h1 className="text-2xl font-bold mb-4 mt-6 text-gray-900">
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-xl font-bold mb-3 mt-5 text-gray-900">
                            {children}
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-lg font-semibold mb-2 mt-4 text-gray-900">
                            {children}
                          </h3>
                        ),
                        // Strong/Bold text
                        strong: ({ children }) => (
                          <strong className="font-bold text-gray-900">
                            {children}
                          </strong>
                        ),
                        // Tables
                        table: ({ children }) => (
                          <div className="my-4 overflow-x-auto">
                            <table className="min-w-full border-collapse border border-gray-300 rounded-lg">
                              {children}
                            </table>
                          </div>
                        ),
                        thead: ({ children }) => (
                          <thead className="bg-gray-50">
                            {children}
                          </thead>
                        ),
                        th: ({ children }) => (
                          <th className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900">
                            {children}
                          </th>
                        ),
                        td: ({ children }) => (
                          <td className="border border-gray-300 px-4 py-2 text-gray-900">
                            {children}
                          </td>
                        ),
                        // Lists
                        ul: ({ children }) => (
                          <ul className="list-disc list-outside ml-6 mb-4 text-gray-900">
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="list-decimal list-outside ml-6 mb-4 text-gray-900">
                            {children}
                          </ol>
                        ),
                        // Paragraphs
                        p: ({ children }) => (
                          <p className="mb-4 text-gray-900 leading-relaxed">
                            {children}
                          </p>
                        ),
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  );
                } catch (error) {
                  console.error('ReactMarkdown error:', error);
                  return (
                    <div className="whitespace-pre-wrap break-words text-gray-900">
                      {message.content}
                    </div>
                  );
                }
              })()}
            </div>
          )}
          {message.isStreaming && (
            <span className="inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse" />
          )}
        </div>
      </div>
    </div>
  );
}
