#!/usr/bin/env python3
"""
Service Startup Script for Nexus AI Chat with Make-it-Heavy Integration

This script helps start both the Python FastAPI service and Next.js application
for development and testing.
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path

class ServiceManager:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        self.python_service_dir = self.base_dir / "make-it-heavy-service"
        self.nextjs_app_dir = self.base_dir / "ai-chat-app"

    def check_prerequisites(self):
        """Check if all required directories and files exist"""
        print("🔍 Checking prerequisites...")
        
        issues = []
        
        # Check directories
        if not self.python_service_dir.exists():
            issues.append(f"Python service directory not found: {self.python_service_dir}")
        
        if not self.nextjs_app_dir.exists():
            issues.append(f"Next.js app directory not found: {self.nextjs_app_dir}")
        
        # Check make-it-heavy
        make_it_heavy_dir = self.base_dir / "make-it-heavy"
        if not make_it_heavy_dir.exists():
            issues.append(f"Make-it-heavy directory not found: {make_it_heavy_dir}")
        
        config_file = make_it_heavy_dir / "config.yaml"
        if not config_file.exists():
            issues.append(f"Make-it-heavy config file not found: {config_file}")
        
        # Check Python requirements
        requirements_file = self.python_service_dir / "requirements.txt"
        if not requirements_file.exists():
            issues.append(f"Python requirements file not found: {requirements_file}")
        
        # Check Next.js package.json
        package_json = self.nextjs_app_dir / "package.json"
        if not package_json.exists():
            issues.append(f"Next.js package.json not found: {package_json}")
        
        if issues:
            print("❌ Prerequisites check failed:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        
        print("✅ Prerequisites check passed")
        return True

    def start_python_service(self):
        """Start the Python FastAPI service"""
        print("🐍 Starting Python FastAPI service...")
        
        try:
            # Change to service directory
            os.chdir(self.python_service_dir)
            
            # Start the service
            process = subprocess.Popen(
                [sys.executable, "start.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append(("Python Service", process))
            
            # Start thread to monitor output
            def monitor_python_output():
                for line in iter(process.stdout.readline, ''):
                    print(f"[Python] {line.rstrip()}")
                process.stdout.close()
            
            thread = threading.Thread(target=monitor_python_output, daemon=True)
            thread.start()
            
            print("✅ Python service started (PID: {})".format(process.pid))
            return True
            
        except Exception as e:
            print(f"❌ Failed to start Python service: {e}")
            return False

    def start_nextjs_app(self):
        """Start the Next.js application"""
        print("⚛️  Starting Next.js application...")
        
        try:
            # Change to app directory
            os.chdir(self.nextjs_app_dir)
            
            # Check if node_modules exists
            if not (self.nextjs_app_dir / "node_modules").exists():
                print("📦 Installing Node.js dependencies...")
                install_process = subprocess.run(
                    ["npm", "install"],
                    capture_output=True,
                    text=True
                )
                if install_process.returncode != 0:
                    print(f"❌ Failed to install dependencies: {install_process.stderr}")
                    return False
                print("✅ Dependencies installed")
            
            # Start the development server
            process = subprocess.Popen(
                ["npm", "run", "dev"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append(("Next.js App", process))
            
            # Start thread to monitor output
            def monitor_nextjs_output():
                for line in iter(process.stdout.readline, ''):
                    print(f"[Next.js] {line.rstrip()}")
                process.stdout.close()
            
            thread = threading.Thread(target=monitor_nextjs_output, daemon=True)
            thread.start()
            
            print("✅ Next.js app started (PID: {})".format(process.pid))
            return True
            
        except Exception as e:
            print(f"❌ Failed to start Next.js app: {e}")
            return False

    def wait_for_services(self):
        """Wait for services to be ready"""
        print("⏳ Waiting for services to be ready...")
        
        # Wait a bit for services to start
        time.sleep(5)
        
        # Check if services are responding
        import requests
        
        # Check Python service
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ Python service is ready")
            else:
                print(f"⚠️  Python service responded with status {response.status_code}")
        except Exception as e:
            print(f"⚠️  Python service not responding: {e}")
        
        # Check Next.js app
        try:
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print("✅ Next.js app is ready")
            else:
                print(f"⚠️  Next.js app responded with status {response.status_code}")
        except Exception as e:
            print(f"⚠️  Next.js app not responding: {e}")

    def cleanup(self):
        """Clean up processes"""
        print("\n🧹 Cleaning up processes...")
        
        for name, process in self.processes:
            if process.poll() is None:  # Process is still running
                print(f"Terminating {name} (PID: {process.pid})")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"Force killing {name}")
                    process.kill()
                except Exception as e:
                    print(f"Error terminating {name}: {e}")

    def run(self):
        """Main run method"""
        print("🚀 Nexus AI Chat Service Manager")
        print("=" * 50)
        
        # Check prerequisites
        if not self.check_prerequisites():
            return False
        
        # Set up signal handler for cleanup
        def signal_handler(signum, frame):
            print("\n🛑 Received interrupt signal")
            self.cleanup()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # Start services
            if not self.start_python_service():
                return False
            
            time.sleep(2)  # Give Python service time to start
            
            if not self.start_nextjs_app():
                return False
            
            # Wait for services to be ready
            self.wait_for_services()
            
            print("\n🎉 All services started successfully!")
            print("📱 Next.js App: http://localhost:3000")
            print("🐍 Python API: http://localhost:8000")
            print("📚 API Docs: http://localhost:8000/docs")
            print("\nPress Ctrl+C to stop all services")
            
            # Keep the script running
            while True:
                time.sleep(1)
                # Check if any process has died
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name} has stopped unexpectedly")
                        return False
                        
        except KeyboardInterrupt:
            print("\n🛑 Received keyboard interrupt")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        finally:
            self.cleanup()
        
        return True

def main():
    """Main entry point"""
    manager = ServiceManager()
    success = manager.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
