import { NextRequest, NextResponse } from 'next/server';
import { APIService } from '@/lib/api';

const PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';

interface EnhancedChatRequest {
  messages: Array<{
    role: string;
    content: string;
  }>;
  mode?: 'single' | 'orchestrator';
  stream?: boolean;
  config?: {
    max_iterations?: number;
    parallel_agents?: number;
    timeout?: number;
    tools_enabled?: boolean;
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: EnhancedChatRequest = await request.json();
    const { messages, mode = 'single', stream = true, config = {} } = body;

    console.log(`Enhanced chat request: mode=${mode}, messages=${messages.length}, stream=${stream}`);

    // Validate request
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Invalid messages format' }, { status: 400 });
    }

    // Determine endpoint based on mode
    const endpoint = mode === 'orchestrator' ? '/chat/orchestrator' : '/chat/single';
    const serviceUrl = `${PYTHON_SERVICE_URL}${endpoint}`;

    // Check if Python service is available
    let serviceAvailable = false;
    try {
      console.log(`Checking Python service health at: ${PYTHON_SERVICE_URL}/health`);
      const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      serviceAvailable = healthCheck.ok;
      console.log(`Python service health check: ${serviceAvailable ? 'OK' : 'FAILED'}`);
    } catch (error) {
      console.warn('Python service health check failed:', error);
      serviceAvailable = false;
    }

    // If service is not available, fallback to direct API (only for single mode)
    if (!serviceAvailable) {
      if (mode === 'orchestrator') {
        return NextResponse.json({ 
          error: 'Enhanced orchestrator mode is currently unavailable. Please try again later or use simple chat mode.' 
        }, { status: 503 });
      }
      
      console.log('Python service unavailable, falling back to direct Chutes API');
      return fallbackToDirectAPI(messages, stream);
    }

    // Forward request to Python service
    console.log(`Forwarding request to Python service: ${serviceUrl}`);
    const response = await fetch(serviceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        mode,
        stream,
        config
      }),
      signal: AbortSignal.timeout(300000) // 5 minute timeout for long operations
    });

    if (!response.ok) {
      console.error(`Python service error: ${response.status} ${response.statusText}`);
      
      // Try fallback for single mode
      if (mode === 'single') {
        console.log('Python service failed, falling back to direct API');
        return fallbackToDirectAPI(messages, stream);
      }
      
      throw new Error(`Python service error: ${response.status}`);
    }

    if (stream && response.body) {
      // Stream response back to client
      return new Response(response.body, {
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'X-Accel-Buffering': 'no', // Disable nginx buffering
        },
      });
    } else {
      const data = await response.json();
      return NextResponse.json(data);
    }

  } catch (error) {
    console.error('Enhanced chat API error:', error);
    
    // Try fallback for single mode if we have the request data
    try {
      const body = await request.clone().json();
      if (body.mode !== 'orchestrator') {
        console.log('Error occurred, attempting fallback to direct API');
        return fallbackToDirectAPI(body.messages, body.stream !== false);
      }
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
    }
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function fallbackToDirectAPI(messages: any[], stream: boolean) {
  console.log('Executing fallback to direct Chutes API');
  
  try {
    if (stream) {
      // Implement streaming fallback
      const encoder = new TextEncoder();
      const readable = new ReadableStream({
        async start(controller) {
          try {
            await APIService.sendMessage(
              messages,
              (chunk: string) => {
                const chunkData = {
                  choices: [{
                    delta: { content: chunk },
                    finish_reason: null
                  }]
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`));
              },
              () => {
                const finalChunk = {
                  choices: [{
                    delta: {},
                    finish_reason: 'stop'
                  }]
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
                controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                controller.close();
              },
              (error: string) => {
                const errorData = { error: { message: error } };
                controller.enqueue(encoder.encode(`data: ${JSON.dumps(errorData)}\n\n`));
                controller.close();
              }
            );
          } catch (error) {
            console.error('Fallback streaming error:', error);
            controller.error(error);
          }
        }
      });

      return new Response(readable, {
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      const response = await APIService.sendNonStreamingMessage(messages);
      return NextResponse.json({ response });
    }
  } catch (error) {
    console.error('Fallback API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Fallback API failed' },
      { status: 500 }
    );
  }
}

// Health check endpoint for the enhanced chat service
export async function GET() {
  try {
    // Check Python service status
    const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    const pythonServiceStatus = healthCheck.ok;
    
    return NextResponse.json({
      status: 'healthy',
      python_service: pythonServiceStatus ? 'available' : 'unavailable',
      fallback: 'available',
      modes: {
        single: pythonServiceStatus ? 'enhanced' : 'fallback',
        orchestrator: pythonServiceStatus ? 'available' : 'unavailable'
      }
    });
  } catch (error) {
    return NextResponse.json({
      status: 'partial',
      python_service: 'unavailable',
      fallback: 'available',
      modes: {
        single: 'fallback',
        orchestrator: 'unavailable'
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
