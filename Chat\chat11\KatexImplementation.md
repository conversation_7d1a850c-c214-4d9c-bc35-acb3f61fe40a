I'll research how to implement KaTeX rendering for mathematical expressions in your AI chat app. Let me start by examining the current codebase structure and then look into KaTeX implementation details.

Now let me research the KaTeX documentation to understand the implementation options:

Let me also check the auto-render extension documentation which is commonly used for rendering math in markdown:

Now let me search for React-specific KaTeX implementations and best practices:

Let me get more specific information about the remark-math and rehype-katex plugins:

Now let me check the current versions and compatibility:

Based on my research, I now have a comprehensive understanding of how to implement KaTeX rendering in your AI chat app. Let me provide you with a detailed implementation guide.

## KaTeX Implementation Guide for Your AI Chat App

Your AI chat app is built with **Next.js 15**, **React 19**, and uses **react-markdown** for rendering assistant messages. Here's how to add KaTeX support for mathematical expressions:

### 1. **Installation**

You'll need to install the required packages:

```bash
npm install remark-math rehype-katex katex
```

- **`remark-math`**: Parses math syntax in markdown (like `$...$` and `$$...$$`)
- **`rehype-katex`**: Renders the parsed math using KaTeX
- **`katex`**: The core KaTeX library for math rendering

### 2. **CSS Import**

KaTeX requires CSS for proper styling. Add this to your layout or component:

````tsx path=Chat/chat11/ai-chat-app/src/app/layout.tsx mode=EXCERPT
import type { Metadata } from "next";
import { Geist, Geist_Mono } from "next/font/google";
import "./globals.css";
import 'katex/dist/katex.min.css'; // Add this line
````

### 3. **Update MessageBubble Component**

Your current `MessageBubble.tsx` already uses `react-markdown`. You need to add the math plugins:

````tsx path=Chat/chat11/ai-chat-app/src/components/MessageBubble.tsx mode=EXCERPT
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math'; // Add this
import rehypeKatex from 'rehype-katex'; // Add this
````

Then update the ReactMarkdown component configuration:

````tsx path=Chat/chat11/ai-chat-app/src/components/MessageBubble.tsx mode=EXCERPT
<ReactMarkdown
  remarkPlugins={[remarkGfm, remarkMath]} // Add remarkMath
  rehypePlugins={[rehypeKatex]} // Add this line
  components={{
    // Your existing components...
  }}
>
  {message.content}
</ReactMarkdown>
````

### 4. **Math Syntax Support**

With this setup, your chat app will support:

- **Inline math**: `$x^2 + y^2 = z^2$`
- **Display math**: `$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$`
- **LaTeX environments**: `\begin{align}...\end{align}`

### 5. **Error Handling**

KaTeX can throw errors for invalid LaTeX. Configure error handling:

```tsx
rehypePlugins={[
  [rehypeKatex, { 
    throwOnError: false, // Render errors in red instead of throwing
    errorColor: '#cc0000',
    strict: false
  }]
]}
```

### 6. **Performance Considerations**

For better performance with many math expressions:

- KaTeX renders faster than MathJax
- Consider lazy loading for messages with heavy math content
- Use persistent macros if you have recurring mathematical definitions

### 7. **Example Usage**

Users can now send messages with math like:

```
The quadratic formula is $x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}$.

For the integral:
$$\int_0^1 x^2 dx = \frac{1}{3}$$
```

### 8. **Styling Integration**

KaTeX math will inherit your existing prose styling from Tailwind's typography plugin. You might want to add custom styles:

```css
.katex {
  font-size: 1.1em;
}

.katex-display {
  margin: 1em 0;
}
```

Would you like me to implement these changes in your codebase? I can update the necessary files to add KaTeX support to your AI chat app.
