'use client';

import { useState, useEffect } from 'react';
import { WrenchScrewdriverIcon } from '@heroicons/react/24/outline';
import { Brain } from 'lucide-react';
import { EnhancedAPIService } from '@/lib/enhanced-api';
import { cn } from '@/lib/utils';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator';

interface CompactModeSelectorProps {
  currentMode: ChatMode;
  onModeChange: (mode: ChatMode) => void;
  disabled?: boolean;
}

export default function CompactModeSelector({ 
  currentMode, 
  onModeChange, 
  disabled = false 
}: CompactModeSelectorProps) {
  const [serviceStatus, setServiceStatus] = useState<any>(null);

  useEffect(() => {
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    try {
      const status = await EnhancedAPIService.checkServiceHealth();
      setServiceStatus(status);
    } catch (error) {
      console.error('Failed to check service status:', error);
      setServiceStatus(null);
    }
  };

  const isEnhancedAvailable = serviceStatus?.modes?.single === 'enhanced';
  const isOrchestratorAvailable = serviceStatus?.modes?.orchestrator === 'available';

  const handleModeSelect = (mode: ChatMode) => {
    if (disabled) return;
    
    // If clicking the currently active mode, deselect it (return to simple)
    if (currentMode === mode && mode !== 'simple') {
      onModeChange('simple');
      return;
    }
    
    if (mode === 'enhanced' || (mode === 'orchestrator' && isOrchestratorAvailable)) {
      onModeChange(mode);
    } else if (mode === 'simple') {
      onModeChange(mode);
    }
  };

  return (
    <div className="flex items-center justify-start gap-1.5 py-1">
      {/* Enhanced Chat Button */}
      <button
        onClick={() => handleModeSelect('enhanced')}
        disabled={disabled}
        className={cn(
          "flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200",
          currentMode === 'enhanced'
            ? "bg-blue-100 text-blue-700 border border-blue-200"
            : "bg-gray-100 text-gray-600 hover:bg-gray-200 border border-transparent",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        title={currentMode === 'enhanced' ? "Click to deselect" : (isEnhancedAvailable ? "Enhanced Chat with Tools" : "Enhanced Chat (Fallback Mode)")}
      >
        <WrenchScrewdriverIcon className="w-3.5 h-3.5" />
        <span>Tools</span>
        {!isEnhancedAvailable && (
          <span className="text-xs bg-yellow-100 text-yellow-700 px-1 py-0.5 rounded text-xs">
            Fallback
          </span>
        )}
      </button>

      {/* Research Mode Button */}
      <button
        onClick={() => handleModeSelect('orchestrator')}
        disabled={disabled || !isOrchestratorAvailable}
        className={cn(
          "flex items-center gap-1.5 px-2.5 py-1 rounded-md text-xs font-medium transition-all duration-200",
          currentMode === 'orchestrator'
            ? "bg-purple-100 text-purple-700 border border-purple-200"
            : isOrchestratorAvailable
            ? "bg-gray-100 text-gray-600 hover:bg-gray-200 border border-transparent"
            : "bg-gray-50 text-gray-400 cursor-not-allowed border border-transparent",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        title={currentMode === 'orchestrator' ? "Click to deselect" : (isOrchestratorAvailable ? "Deep Research Mode" : "Research Mode (Unavailable)")}
      >
        <Brain className="w-3.5 h-3.5" />
        <span>Deep Research</span>
        {!isOrchestratorAvailable && (
          <span className="text-xs bg-gray-100 text-gray-500 px-1 py-0.5 rounded text-xs">
            Unavailable
          </span>
        )}
      </button>
    </div>
  );
}