'use client';

import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ChatState } from '@/types/chat';
import { StorageService } from '@/lib/storage';
import { APIService } from '@/lib/api';
import { EnhancedAPIService, EnhancedChatConfig } from '@/lib/enhanced-api';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator';

interface EnhancedChatState extends ChatState {
  currentMode: ChatMode;
  progressStatus?: string;
  serviceHealth?: any;
}

export function useEnhancedChat() {
  const [state, setState] = useState<EnhancedChatState>({
    conversations: [],
    currentConversationId: null,
    isLoading: false,
    error: null,
    sidebarOpen: false,
    currentMode: 'simple',
    progressStatus: undefined,
    serviceHealth: null
  });

  // Load conversations and check service health on mount
  useEffect(() => {
    const conversations = StorageService.loadConversations();
    const currentConversationId = StorageService.loadCurrentConversationId();
    
    setState(prev => ({
      ...prev,
      conversations,
      currentConversationId: conversations.find(c => c.id === currentConversationId) ? currentConversationId : null
    }));

    // Check service health
    checkServiceHealth();
  }, []);

  const checkServiceHealth = useCallback(async () => {
    try {
      const health = await EnhancedAPIService.checkServiceHealth();
      setState(prev => ({ ...prev, serviceHealth: health }));
    } catch (error) {
      console.error('Failed to check service health:', error);
      setState(prev => ({ ...prev, serviceHealth: null }));
    }
  }, []);

  const getCurrentConversation = useCallback((): Conversation | null => {
    if (!state.currentConversationId) return null;
    return state.conversations.find(c => c.id === state.currentConversationId) || null;
  }, [state.conversations, state.currentConversationId]);

  const createNewConversation = useCallback((title?: string): string => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: title || 'New Chat',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversationId: newConversation.id,
      error: null
    }));

    StorageService.updateConversation(newConversation);
    StorageService.saveCurrentConversationId(newConversation.id);

    return newConversation.id;
  }, []);

  const selectConversation = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      currentConversationId: id,
      error: null
    }));
    StorageService.saveCurrentConversationId(id);
  }, []);

  const deleteConversation = useCallback((id: string) => {
    setState(prev => {
      const newConversations = prev.conversations.filter(c => c.id !== id);
      const newCurrentId = prev.currentConversationId === id 
        ? (newConversations.length > 0 ? newConversations[0].id : null)
        : prev.currentConversationId;

      return {
        ...prev,
        conversations: newConversations,
        currentConversationId: newCurrentId,
        error: null
      };
    });

    StorageService.deleteConversation(id);
  }, []);

  const renameConversation = useCallback((id: string, newTitle: string) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => 
        c.id === id 
          ? { ...c, title: newTitle, updatedAt: Date.now() }
          : c
      )
    }));

    const conversation = state.conversations.find(c => c.id === id);
    if (conversation) {
      const updatedConversation = { ...conversation, title: newTitle, updatedAt: Date.now() };
      StorageService.updateConversation(updatedConversation);
    }
  }, [state.conversations]);

  const setMode = useCallback((mode: ChatMode) => {
    setState(prev => ({ ...prev, currentMode: mode, error: null }));
  }, []);

  const sendMessage = useCallback(async (content: string, config?: Partial<EnhancedChatConfig>) => {
    if (!content.trim()) return;

    // Get current state values
    let conversationId: string;
    let currentConversations: Conversation[];
    let currentMode: ChatMode;

    // Update state to get current values and create conversation if needed
    await new Promise<void>((resolve) => {
      setState(prev => {
        conversationId = prev.currentConversationId;
        currentConversations = prev.conversations;
        currentMode = prev.currentMode;

        // Create new conversation if none exists
        if (!conversationId) {
          const newConversation: Conversation = {
            id: uuidv4(),
            title: content.slice(0, 50),
            messages: [],
            createdAt: Date.now(),
            updatedAt: Date.now()
          };

          conversationId = newConversation.id;
          currentConversations = [newConversation, ...prev.conversations];

          StorageService.updateConversation(newConversation);
          StorageService.saveCurrentConversationId(newConversation.id);

          resolve();
          return {
            ...prev,
            conversations: currentConversations,
            currentConversationId: conversationId,
            error: null
          };
        }

        resolve();
        return prev;
      });
    });

    const conversation = currentConversations.find(c => c.id === conversationId);
    console.log('useEnhancedChat: Looking for conversation:', {
      conversationId,
      availableConversations: currentConversations.map(c => c.id),
      found: !!conversation
    });

    if (!conversation) {
      console.error('Conversation not found:', conversationId);
      return;
    }

    const userMessage: Message = {
      id: uuidv4(),
      role: 'user',
      content: content.trim(),
      timestamp: Date.now()
    };

    const assistantMessage: Message = {
      id: uuidv4(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
      isStreaming: true
    };

    // Add messages to conversation
    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, userMessage, assistantMessage],
      updatedAt: Date.now(),
      title: conversation.messages.length === 0 ? content.slice(0, 50) : conversation.title
    };

    // Update state and save to storage immediately
    setState(prev => {
      const newState = {
        ...prev,
        conversations: prev.conversations.map(c =>
          c.id === conversationId ? updatedConversation : c
        ),
        isLoading: true,
        error: null,
        progressStatus: undefined
      };

      // Update the local currentConversations variable to match the new state
      currentConversations = newState.conversations;

      return newState;
    });

    // Save updated conversation to storage
    StorageService.updateConversation(updatedConversation);

    console.log('useEnhancedChat: Added messages to state:', {
      conversationId,
      userMessageId: userMessage.id,
      assistantMessageId: assistantMessage.id,
      totalMessages: updatedConversation.messages.length
    });

    try {
      const messages = [...conversation.messages, userMessage].map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }));

      let fullResponse = '';
      console.log(`Sending message in ${currentMode} mode...`);

      // Use appropriate API based on mode
      if (currentMode === 'simple') {
        // Use original API service for simple mode
        await APIService.sendMessage(
          messages,
          // onChunk
          (chunk: string) => {
            fullResponse += chunk;
            setState(prev => ({
              ...prev,
              conversations: prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse }
                          : m
                      )
                    }
                  : c
              )
            }));
          },
          // onComplete
          () => {
            console.log('Simple mode message completed');
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse, isStreaming: false }
                          : m
                      ),
                      updatedAt: Date.now()
                    }
                  : c
              );

              // Save to storage
              const finalConversation = updatedConversations.find(c => c.id === conversationId);
              if (finalConversation) {
                StorageService.updateConversation(finalConversation);
              }

              return {
                ...prev,
                conversations: updatedConversations,
                isLoading: false,
                progressStatus: undefined
              };
            });
          },
          // onError
          (error: string) => {
            console.error('Simple mode API error:', error);
            setState(prev => ({
              ...prev,
              conversations: prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.filter(m => m.id !== assistantMessage.id)
                    }
                  : c
              ),
              isLoading: false,
              error,
              progressStatus: undefined
            }));
          }
        );
      } else {
        // Use enhanced API service for enhanced and orchestrator modes
        const enhancedConfig: EnhancedChatConfig = {
          mode: currentMode === 'orchestrator' ? 'orchestrator' : 'single',
          maxIterations: config?.maxIterations || 3,
          parallelAgents: config?.parallelAgents || 3,
          timeout: config?.timeout || 300000,
          toolsEnabled: config?.toolsEnabled !== false
        };

        await EnhancedAPIService.sendEnhancedMessage(
          messages,
          enhancedConfig,
          // onChunk
          (chunk: string) => {
            fullResponse += chunk;
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse }
                          : m
                      )
                    }
                  : c
              );

              // Update the conversation in storage
              const updatedConversation = updatedConversations.find(c => c.id === conversationId);
              if (updatedConversation) {
                StorageService.updateConversation(updatedConversation);
              }

              return {
                ...prev,
                conversations: updatedConversations
              };
            });
          },
          // onComplete
          () => {
            console.log(`${currentMode} mode message completed, fullResponse length:`, fullResponse.length);
            setState(prev => {
              console.log('onComplete: Updating state with final response');
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse, isStreaming: false }
                          : m
                      ),
                      updatedAt: Date.now()
                    }
                  : c
              );

              console.log('onComplete: Updated conversations:', updatedConversations.map(c => ({
                id: c.id,
                messageCount: c.messages.length,
                messages: c.messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
              })));

              // Save to storage
              const finalConversation = updatedConversations.find(c => c.id === conversationId);
              if (finalConversation) {
                StorageService.updateConversation(finalConversation);
              }

              return {
                ...prev,
                conversations: updatedConversations,
                isLoading: false,
                progressStatus: undefined
              };
            });
          },
          // onError
          (error: string) => {
            console.error(`${currentMode} mode API error:`, error);
            console.error('onError: Removing assistant message and updating state');
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.filter(m => m.id !== assistantMessage.id)
                    }
                  : c
              );

              console.error('onError: Updated conversations after error:', updatedConversations.map(c => ({
                id: c.id,
                messageCount: c.messages.length,
                messages: c.messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
              })));

              return {
                ...prev,
                conversations: updatedConversations,
                isLoading: false,
                error,
                progressStatus: undefined
              };
            });
          },
          // onProgress (for orchestrator mode)
          (status: string) => {
            console.log('Progress update:', status);
            setState(prev => ({ ...prev, progressStatus: status }));
          }
        );
      }
    } catch (error) {
      console.error('Send message error (catch block):', error);
      console.error('catch: Removing assistant message and updating state');
      setState(prev => {
        const updatedConversations = prev.conversations.map(c =>
          c.id === conversationId
            ? {
                ...c,
                messages: c.messages.filter(m => m.id !== assistantMessage.id)
              }
            : c
        );

        console.error('catch: Updated conversations after error:', updatedConversations.map(c => ({
          id: c.id,
          messageCount: c.messages.length,
          messages: c.messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
        })));

        return {
          ...prev,
          conversations: updatedConversations,
          isLoading: false,
          error: error instanceof Error ? error.message : 'An error occurred',
          progressStatus: undefined
        };
      });
    }
  }, []);

  const toggleSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarOpen: !prev.sidebarOpen }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    currentConversation: getCurrentConversation(),
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    setMode,
    toggleSidebar,
    clearError,
    checkServiceHealth
  };
}
