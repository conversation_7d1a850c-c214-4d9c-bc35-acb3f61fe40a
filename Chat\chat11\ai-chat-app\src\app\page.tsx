'use client';

import { useEnhancedChat } from '@/hooks/useEnhancedChat';
import Sidebar from '@/components/Sidebar';
import ChatArea from '@/components/ChatArea';
import ChatInput from '@/components/ChatInput';

export default function Home() {
  const {
    conversations,
    currentConversation,
    currentConversationId,
    isLoading,
    error,
    sidebarOpen,
    currentMode,
    progressStatus,
    serviceHealth,
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    setMode,
    toggleSidebar,
    clearError,
    checkServiceHealth
  } = useEnhancedChat();

  const handleNewChat = () => {
    createNewConversation();
  };

  const handleSendMessage = (message: string) => {
    console.log('Page: Sending message:', message, 'Mode:', currentMode);
    sendMessage(message);
  };

  // Debug logging for current conversation
  console.log('Page render:', {
    currentConversationId,
    currentConversation: currentConversation ? {
      id: currentConversation.id,
      messageCount: currentConversation.messages.length,
      messages: currentConversation.messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
    } : null,
    isLoading,
    error,
    currentMode
  });

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        conversations={conversations}
        currentConversationId={currentConversationId}
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
        onNewChat={handleNewChat}
        onSelectConversation={selectConversation}
        onDeleteConversation={deleteConversation}
        onRenameConversation={renameConversation}
      />

      <div className="flex-1 flex flex-col">
        <ChatArea
          messages={currentConversation?.messages || []}
          isLoading={isLoading}
          error={error}
          currentMode={currentMode}
          progressStatus={progressStatus}
        />

        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={isLoading}
          currentMode={currentMode}
          onModeChange={setMode}
          placeholder={
            !process.env.NEXT_PUBLIC_CHUTES_API_TOKEN
              ? "Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable..."
              : currentMode === 'simple'
              ? "Type your message..."
              : currentMode === 'enhanced'
              ? "Ask me anything - I have access to tools and web search..."
              : "Ask me a complex research question..."
          }
        />
      </div>
    </div>
  );
}
