# Make-it-Heavy FastAPI Service

This service provides REST API endpoints for the Make-it-Heavy agent and orchestrator functionality, designed to integrate with the Nexus AI chat application.

## Features

- **Single Agent Mode**: `/chat/single` - Enhanced chat with tool support
- **Orchestrator Mode**: `/chat/orchestrator` - Multi-agent research and analysis
- **Streaming Support**: Real-time response streaming compatible with OpenAI format
- **Health Monitoring**: `/health` endpoint for service status
- **Tool Discovery**: `/tools` endpoint to list available tools
- **Configuration**: `/config` endpoint for current settings

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Ensure Make-it-Heavy Directory**:
   Make sure the `../make-it-heavy` directory exists with:
   - `config.yaml` - Configuration file
   - `agent.py` - Agent implementation
   - `orchestrator.py` - Orchestrator implementation
   - `tools/` - Tool directory

## Running the Service

### Development Mode
```bash
python start.py
```

### Production Mode
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Using Docker (Optional)
```bash
docker build -t make-it-heavy-service .
docker run -p 8000:8000 make-it-heavy-service
```

## API Endpoints

### POST /chat/single
Single agent chat with tool support.

**Request**:
```json
{
  "messages": [
    {"role": "user", "content": "Your question here"}
  ],
  "stream": true,
  "config": {
    "max_iterations": 10,
    "tools_enabled": true
  }
}
```

### POST /chat/orchestrator
Multi-agent orchestration for complex queries.

**Request**:
```json
{
  "messages": [
    {"role": "user", "content": "Complex research query"}
  ],
  "stream": true,
  "config": {
    "parallel_agents": 4,
    "timeout": 300
  }
}
```

### GET /health
Service health check.

**Response**:
```json
{
  "status": "healthy",
  "service": "make-it-heavy",
  "version": "1.0.0"
}
```

### GET /tools
List available tools.

**Response**:
```json
{
  "tools": ["search_tool", "calculator_tool", "read_file_tool", "write_file_tool"],
  "count": 4
}
```

### GET /config
Current configuration (safe values only).

**Response**:
```json
{
  "agent": {
    "max_iterations": 10
  },
  "orchestrator": {
    "parallel_agents": 4,
    "task_timeout": 300
  },
  "tools_count": 4
}
```

## Integration with Next.js

The service is designed to work with the Nexus AI chat app through the `/api/enhanced-chat` proxy endpoint. The Next.js app will:

1. Send requests to `/api/enhanced-chat`
2. The Next.js API route proxies to this service
3. Responses are streamed back to the frontend
4. If this service is unavailable, fallback to direct Chutes API

## Development Notes

- The service automatically discovers tools from the `make-it-heavy/tools/` directory
- Streaming responses use Server-Sent Events (SSE) format compatible with OpenAI
- CORS is configured for Next.js development server
- All errors are properly handled and returned in a consistent format

## Troubleshooting

1. **Import Errors**: Ensure the `make-it-heavy` directory is in the correct location
2. **Config Not Found**: Check that `config.yaml` exists in the make-it-heavy directory
3. **CORS Issues**: Update `CORS_ORIGINS` in `.env` file
4. **Port Conflicts**: Change `PORT` in `.env` file if 8000 is in use
