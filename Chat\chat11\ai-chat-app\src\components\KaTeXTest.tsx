'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';

const testContent = `# KaTeX Test

## Inline Math Examples
The quadratic formula is $x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$.

<PERSON><PERSON><PERSON>'s identity: $e^{i\\pi} + 1 = 0$

## Display Math Examples

The Gaussian integral:
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

Summation:
$$\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}$$

## Matrix Example
$$\\begin{pmatrix}
a & b \\\\
c & d
\\end{pmatrix}$$

## Aligned Equations
$$\\begin{align}
f(x) &= ax^2 + bx + c \\\\
f'(x) &= 2ax + b \\\\
f''(x) &= 2a
\\end{align}$$
`;

export default function KaTeXTest() {
  return (
    <div className="max-w-4xl mx-auto p-8 bg-white">
      <div className="prose prose-gray prose-sm max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={[
            [rehypeKatex, {
              throwOnError: false,
              errorColor: '#cc0000',
              strict: false
            }]
          ]}
        >
          {testContent}
        </ReactMarkdown>
      </div>
    </div>
  );
}
