import { NextRequest, NextResponse } from 'next/server';

const PYTHON_SERVICE_URL = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';

export async function GET() {
  try {
    console.log('Checking enhanced chat service status');
    
    // Check Python service detailed status
    const response = await fetch(`${PYTHON_SERVICE_URL}/status`, {
      method: 'GET',
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`Python service error: ${response.status}`);
    }

    const pythonStatus = await response.json();
    
    // Return combined status
    return NextResponse.json({
      enhanced_chat: {
        status: 'healthy',
        python_service: pythonStatus,
        next_js_proxy: {
          status: 'healthy',
          endpoints: [
            '/api/enhanced-chat',
            '/api/enhanced-chat/status',
            '/api/enhanced-chat/tools'
          ]
        },
        features: {
          single_agent: 'available',
          orchestrator: 'available',
          streaming: 'available',
          fallback: 'available'
        }
      }
    });
  } catch (error) {
    console.error('Error checking enhanced chat status:', error);
    
    // Return partial status with fallback info
    return NextResponse.json({
      enhanced_chat: {
        status: 'partial',
        python_service: {
          status: 'unavailable',
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        next_js_proxy: {
          status: 'healthy',
          endpoints: [
            '/api/enhanced-chat',
            '/api/enhanced-chat/status',
            '/api/enhanced-chat/tools'
          ]
        },
        features: {
          single_agent: 'fallback_only',
          orchestrator: 'unavailable',
          streaming: 'fallback_only',
          fallback: 'available'
        }
      }
    }, { status: 503 });
  }
}
