# Chutes AI API Configuration (Required)
# Get your API token from https://chutes.ai
NEXT_PUBLIC_CHUTES_API_TOKEN=your_chutes_api_token_here

# Enhanced Chat Service Configuration
# URL of the Python FastAPI service for enhanced features
PYTHON_SERVICE_URL=http://localhost:8000

# Optional: Custom API endpoints
# CHUTES_API_URL=https://api.chutes.ai/v1

# Development Configuration
# Set to 'development' for detailed logging
NODE_ENV=development

# Optional: Service Health Check Settings
# How often to check Python service health (in milliseconds)
SERVICE_HEALTH_CHECK_INTERVAL=30000

# Optional: Request Timeout Settings
# Timeout for Python service requests (in milliseconds)
PYTHON_SERVICE_TIMEOUT=300000

# Optional: Fallback Configuration
# Whether to enable automatic fallback to direct API when Python service is unavailable
ENABLE_FALLBACK=true
