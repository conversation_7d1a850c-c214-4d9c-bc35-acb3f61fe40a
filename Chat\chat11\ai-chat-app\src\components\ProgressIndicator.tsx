'use client';

import { useState, useEffect } from 'react';

interface ProgressIndicatorProps {
  isVisible: boolean;
  currentStatus?: string;
  mode?: 'enhanced' | 'orchestrator';
}

export default function ProgressIndicator({ 
  isVisible, 
  currentStatus, 
  mode = 'enhanced' 
}: ProgressIndicatorProps) {
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  const getStatusIcon = (status: string) => {
    if (status.includes('🚀') || status.includes('Starting')) return '🚀';
    if (status.includes('🧠') || status.includes('Breaking')) return '🧠';
    if (status.includes('⚡') || status.includes('Launching')) return '⚡';
    if (status.includes('🔄') || status.includes('Synthesizing')) return '🔄';
    if (status.includes('🔍') || status.includes('search')) return '🔍';
    if (status.includes('🧮') || status.includes('calculat')) return '🧮';
    if (status.includes('📁') || status.includes('file')) return '📁';
    return '⚙️';
  };

  const getModeConfig = () => {
    if (mode === 'orchestrator') {
      return {
        title: 'Research Mode Active',
        subtitle: 'Multi-agent analysis in progress',
        color: 'purple',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        textColor: 'text-purple-800',
        iconColor: 'text-purple-600'
      };
    }
    
    return {
      title: 'Enhanced Mode Active',
      subtitle: 'AI tools and capabilities enabled',
      color: 'blue',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      iconColor: 'text-blue-600'
    };
  };

  const config = getModeConfig();

  return (
    <div className={`
      ${config.bgColor} ${config.borderColor} border rounded-lg p-4 mb-4
      animate-pulse
    `}>
      <div className="flex items-start space-x-3">
        {/* Animated spinner */}
        <div className="flex-shrink-0">
          <div className={`
            w-6 h-6 border-2 border-${config.color}-200 border-t-${config.color}-600 
            rounded-full animate-spin
          `}></div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className={`text-sm font-medium ${config.textColor}`}>
              {config.title}
            </h4>
            <span className="text-xs text-gray-500">
              {dots}
            </span>
          </div>
          
          <p className="text-xs text-gray-600 mb-2">
            {config.subtitle}
          </p>

          {currentStatus && (
            <div className="flex items-center space-x-2">
              <span className="text-sm">
                {getStatusIcon(currentStatus)}
              </span>
              <span className="text-sm text-gray-700">
                {currentStatus}
              </span>
            </div>
          )}

          {mode === 'orchestrator' && !currentStatus && (
            <div className="space-y-1 mt-2">
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>Analyzing query complexity</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-100"></div>
                <span>Deploying specialized agents</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <div className="w-2 h-2 bg-purple-200 rounded-full animate-pulse delay-200"></div>
                <span>Gathering comprehensive insights</span>
              </div>
            </div>
          )}

          {mode === 'enhanced' && !currentStatus && (
            <div className="space-y-1 mt-2">
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Processing with AI tools</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-gray-600">
                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100"></div>
                <span>Accessing real-time information</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Progress bar for orchestrator mode */}
      {mode === 'orchestrator' && (
        <div className="mt-3">
          <div className="w-full bg-purple-200 rounded-full h-1.5">
            <div className="bg-purple-600 h-1.5 rounded-full animate-pulse" 
                 style={{ width: '60%' }}></div>
          </div>
        </div>
      )}
    </div>
  );
}
