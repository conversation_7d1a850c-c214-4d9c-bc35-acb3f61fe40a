'use client';

import { useEffect, useRef } from 'react';
import { Message } from '@/types/chat';
import MessageBubble from './MessageBubble';
import ProgressIndicator from './ProgressIndicator';
import { cn } from '@/lib/utils';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator';

interface ChatAreaProps {
  messages: Message[];
  isLoading?: boolean;
  error?: string | null;
  currentMode?: ChatMode;
  progressStatus?: string;
}

export default function ChatArea({
  messages,
  isLoading = false,
  error,
  currentMode = 'simple',
  progressStatus
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Debug logging
  console.log('ChatArea render:', {
    messageCount: messages.length,
    isLoading,
    error,
    currentMode,
    progressStatus,
    messages: messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (messages.length === 0 && !isLoading && !error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl font-bold">N</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome to Nexus
          </h2>
          <p className="text-gray-600 mb-6">
            Your AI-powered chat assistant with enhanced capabilities. Choose your mode and start a conversation.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <p>• <strong>Simple Chat:</strong> Fast AI responses</p>
            <p>• <strong>Enhanced Chat:</strong> AI with tools and web search</p>
            <p>• <strong>Research Mode:</strong> Multi-agent deep analysis</p>
            <p>• Your conversations are saved locally</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-white">
      <div className="max-w-4xl mx-auto">
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}

        {/* Enhanced progress indicator for enhanced and orchestrator modes */}
        {isLoading && (currentMode === 'enhanced' || currentMode === 'orchestrator') && (
          <div className="p-4">
            <ProgressIndicator
              isVisible={true}
              currentStatus={progressStatus}
              mode={currentMode === 'orchestrator' ? 'orchestrator' : 'enhanced'}
            />
          </div>
        )}

        {/* Simple loading indicator for simple mode */}
        {isLoading && currentMode === 'simple' && (
          <div className="flex gap-4 p-4 bg-gray-50">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-600 flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-sm">Nexus</span>
                <span className="text-xs text-gray-500 bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                  Simple Mode
                </span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </div>
        )}

        {/* Enhanced error handling */}
        {error && (
          <div className="p-4 mx-4 mb-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="text-red-800 font-medium">Error</p>
                    {currentMode !== 'simple' && (
                      <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full">
                        {currentMode === 'orchestrator' ? 'Research Mode' : 'Enhanced Mode'}
                      </span>
                    )}
                  </div>
                  <p className="text-red-600 text-sm mb-2">{error}</p>

                  {/* Show fallback info for enhanced modes */}
                  {currentMode !== 'simple' && error.includes('unavailable') && (
                    <div className="text-xs text-red-500 bg-red-100 p-2 rounded border-l-2 border-red-300">
                      <p className="font-medium mb-1">💡 Suggestion:</p>
                      <p>Try switching to Simple Chat mode for basic AI responses, or check if the enhanced service is running.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
